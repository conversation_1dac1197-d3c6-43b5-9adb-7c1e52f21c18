import SwiftUI

struct LicenseSettingsView: View {
    @StateObject private var licenseManager = LicenseManager.shared
    @State private var licenseKeyInput: String = ""
    @State private var emailInput: String = ""
    @State private var showingClearConfirmation = false

    var body: some View {
        VStack(spacing: 0) {
            ScrollView {
                VStack(alignment: .leading, spacing: 16) {
                    // License Status Section
                    Text("License Status")
                        .snapbackSectionTitleStyle()

                    GroupBox {
                        VStack(spacing: 0) {
                            // Current Status Row
                            HStack {
                                Image(systemName: statusIcon)
                                    .foregroundColor(licenseManager.licenseStatus.color)
                                    .frame(width: 16)

                                Text("Status")

                                Spacer()

                                Text(licenseManager.licenseStatus.displayName)
                                    .foregroundColor(licenseManager.licenseStatus.color)
                                    .font(
                                        .system(size: SnapbackTheme.FontSize.body, weight: .medium))
                            }
                            .snapbackRowStyle()

                            // Trial Information (if on active trial)
                            if licenseManager.licenseStatus == .trial
                                && licenseManager.isTrialActive
                            {
                                Divider()

                                HStack {
                                    Image(systemName: "clock")
                                        .foregroundColor(.orange)
                                        .frame(width: 16)

                                    Text("Trial Days Remaining")

                                    Spacer()

                                    Text("\(licenseManager.remainingTrialDays) days")
                                        .foregroundColor(.orange)
                                        .font(
                                            .system(
                                                size: SnapbackTheme.FontSize.body, weight: .medium))
                                }
                                .snapbackRowStyle()
                            }

                            // License Information (if available)
                            if let info = licenseManager.licenseInfo {
                                Divider()

                                VStack(spacing: 0) {
                                    // License Type
                                    HStack {
                                        Image(systemName: "doc.text")
                                            .foregroundColor(.secondary)
                                            .frame(width: 16)

                                        Text("License Type")

                                        Spacer()

                                        Text(info.licenseType)
                                            .foregroundColor(.secondary)
                                    }
                                    .snapbackRowStyle()

                                    Divider()

                                    // Registered User
                                    HStack {
                                        Image(systemName: "person")
                                            .foregroundColor(.secondary)
                                            .frame(width: 16)

                                        Text("Registered To")

                                        Spacer()

                                        Text(info.registeredUser)
                                            .foregroundColor(.secondary)
                                    }
                                    .snapbackRowStyle()

                                    Divider()

                                    // Expiration Date
                                    HStack {
                                        Image(systemName: "calendar")
                                            .foregroundColor(.secondary)
                                            .frame(width: 16)

                                        Text("Expires")

                                        Spacer()

                                        Text(info.expirationDisplayText)
                                            .foregroundColor(info.isExpired ? .red : .secondary)
                                    }
                                    .snapbackRowStyle()
                                }
                            }
                        }
                    }

                    // License Key Section
                    Text("License Information")
                        .snapbackSectionTitleStyle()

                    GroupBox {
                        VStack(spacing: 0) {
                            // Email Input Row - following General tab pattern
                            VStack(alignment: .leading, spacing: 4) {
                                HStack {
                                    Text("Email Address")

                                    Spacer()

                                    if licenseManager.isValidating {
                                        ProgressView()
                                            .scaleEffect(0.8)
                                    } else {
                                        TextField("<EMAIL>", text: $emailInput)
                                            .textFieldStyle(.roundedBorder)
                                            .disabled(
                                                licenseManager.isValidating
                                                    || (licenseManager.licenseStatus == .valid
                                                        && !licenseManager.licenseKey.isEmpty)
                                            )
                                            .disableAutocorrection(true)
                                            .textContentType(.emailAddress)
                                            .frame(width: 200)
                                    }
                                }

                                if licenseManager.licenseStatus == .valid
                                    && !licenseManager.licenseKey.isEmpty
                                {
                                    Text(
                                        "Email and license key are locked when a valid license is active."
                                    )
                                    .snapbackCaptionStyle()
                                }
                            }
                            .snapbackRowStyle()

                            Divider()

                            // License Key Input Row - following General tab pattern
                            VStack(alignment: .leading, spacing: 4) {
                                HStack {
                                    Text("License Key")

                                    Spacer()

                                    TextField("XXXX-XXXX-XXXX-XXXX", text: $licenseKeyInput)
                                        .textFieldStyle(.roundedBorder)
                                        .font(.system(.body, design: .monospaced))
                                        .disabled(
                                            licenseManager.isValidating
                                                || (licenseManager.licenseStatus == .valid
                                                    && !licenseManager.licenseKey.isEmpty)
                                        )
                                        .disableAutocorrection(true)
                                        .frame(width: 200)
                                        .onSubmit {
                                            Task {
                                                await licenseManager.setLicenseKeyAndEmail(
                                                    licenseKeyInput, email: emailInput)
                                            }
                                        }
                                        .onChange(of: licenseKeyInput) { oldValue, newValue in
                                            // Auto-format license key with dashes, but preserve trial key format
                                            if newValue.hasPrefix("TRIAL-") {
                                                // Don't reformat trial keys - they have their own format
                                                return
                                            }

                                            let cleaned = newValue.replacingOccurrences(
                                                of: "-", with: ""
                                            ).uppercased()
                                            if cleaned.count <= 16 {
                                                let formatted = formatLicenseKey(cleaned)
                                                if formatted != newValue {
                                                    licenseKeyInput = formatted
                                                }
                                            } else {
                                                licenseKeyInput = oldValue
                                            }
                                        }
                                }
                            }
                            .snapbackRowStyle()

                            Divider()

                            // Status Display - following General tab pattern
                            VStack(alignment: .leading, spacing: 4) {
                                HStack {
                                    Text("Status")

                                    Spacer()

                                    switch licenseManager.licenseStatus {
                                    case .valid:
                                        HStack(spacing: 4) {
                                            Image(systemName: "checkmark.circle.fill")
                                                .foregroundColor(.green)
                                            Text("Valid")
                                                .foregroundColor(.green)
                                        }
                                    case .invalid:
                                        HStack(spacing: 4) {
                                            Image(systemName: "xmark.circle.fill")
                                                .foregroundColor(.red)
                                            Text("Invalid")
                                                .foregroundColor(.red)
                                        }
                                    case .expired:
                                        HStack(spacing: 4) {
                                            Image(systemName: "clock.fill")
                                                .foregroundColor(.orange)
                                            Text("Expired")
                                                .foregroundColor(.orange)
                                        }
                                    case .unlicensed:
                                        HStack(spacing: 4) {
                                            Image(systemName: "minus.circle.fill")
                                                .foregroundColor(.secondary)
                                            Text("No License")
                                                .foregroundColor(.secondary)
                                        }
                                        case .trial:
                                            <#code#>
                                        case .trial:
                                            <#code#>
                                        case .trial:
                                            <#code#>
                                        case .trial:
                                            <#code#>
                                        case .trial:
                                            <#code#>
                                        case .trial:
                                            <#code#>
                                        case .trial:
                                            <#code#>
                                        case .trial:
                                            <#code#>
                                        case .trial:
                                            <#code#>
                                        case .trial:
                                            <#code#>
                                        case .trial:
                                            <#code#>
                                        case .trial:
                                            <#code#>
                                        case .trial:
                                            <#code#>
                                        case .trial:
                                            <#code#>
                                        case .trial:
                                            <#code#>
                                        case .trial:
                                            <#code#>
                                        case .trial:
                                            <#code#>
                                    }
                                }

                                // Error Message
                                if let error = licenseManager.lastError {
                                    Text(error)
                                        .snapbackCaptionStyle()
                                        .foregroundColor(.red)
                                }

                                // Action Buttons - only show when not licensed or when there's an error
                                if licenseManager.licenseStatus != .valid
                                    || licenseManager.licenseKey.isEmpty
                                {
                                    HStack(spacing: 12) {
                                        // Only show Validate button when not already licensed
                                        if licenseManager.licenseStatus != .valid
                                            || licenseManager.licenseKey.isEmpty
                                        {
                                            Button("Validate License") {
                                                Task {
                                                    await licenseManager.setLicenseKeyAndEmail(
                                                        licenseKeyInput, email: emailInput)
                                                }
                                            }
                                            .buttonStyle(.bordered)
                                            .disabled(
                                                licenseKeyInput.isEmpty || emailInput.isEmpty
                                                    || licenseManager.isValidating)
                                        }

                                        // Clear Button (only show if there's a license)
                                        if !licenseManager.licenseKey.isEmpty {
                                            Button("Clear License") {
                                                showingClearConfirmation = true
                                            }
                                            .buttonStyle(.bordered)
                                            .foregroundColor(.red)
                                        }

                                        Spacer()
                                    }
                                    .padding(.top, 8)
                                }
                            }
                            .snapbackRowStyle()
                        }
                    }

                    // Help Section
                    Text("Help")
                        .snapbackSectionTitleStyle()

                    GroupBox {
                        VStack(spacing: 0) {
                            VStack(alignment: .leading, spacing: 4) {
                                Text("Need help with licensing?")
                                    .font(
                                        .system(size: SnapbackTheme.FontSize.body, weight: .medium))

                                Text(
                                    "Contact support for license issues, visit our website for purchasing options, or check your email for license key delivery."
                                )
                                .snapbackCaptionStyle()

                                HStack(spacing: 12) {
                                    Button("Contact Support") {
                                        if let url = URL(string: "mailto:<EMAIL>") {
                                            NSWorkspace.shared.open(url)
                                        }
                                    }
                                    .buttonStyle(.bordered)

                                    Button("Purchase License") {
                                        if let url = URL(string: "https://snapbackapp.com/purchase")
                                        {
                                            NSWorkspace.shared.open(url)
                                        }
                                    }
                                    .buttonStyle(.bordered)

                                    Spacer()
                                }
                                .padding(.top, 8)
                            }
                            .snapbackRowStyle()
                        }
                    }

                    Spacer(minLength: 20)
                }
                .padding()
            }

            // Footer with Reset Button
            SettingsFooter(buttonTitle: "Reset License Settings") {
                showingClearConfirmation = true
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .onAppear {
            // Load current license key and email into input fields
            licenseKeyInput = licenseManager.licenseKey
            emailInput = licenseManager.userEmail
        }
        .onChange(of: licenseManager.licenseKey) { _, newKey in
            // Update input field when license key changes (e.g., trial activation)
            licenseKeyInput = newKey
        }
        .onChange(of: licenseManager.userEmail) { _, newEmail in
            // Update input field when email changes (e.g., trial activation)
            emailInput = newEmail
        }
        .alert("Clear License", isPresented: $showingClearConfirmation) {
            Button("Cancel", role: .cancel) {}
            Button("Clear", role: .destructive) {
                licenseManager.clearLicense()
                licenseKeyInput = ""
                emailInput = ""
            }
        } message: {
            Text(
                "Are you sure you want to clear the current license? This action cannot be undone.")
        }
    }

    // MARK: - Helper Properties

    private var statusIcon: String {
        switch licenseManager.licenseStatus {
        case .unlicensed:
            return "exclamationmark.triangle"
        case .valid:
            return "checkmark.circle.fill"
        case .invalid:
            return "xmark.circle.fill"
        case .expired:
            return "clock.badge.exclamationmark"
        case .trial:
            return "clock"
        }

    }
}

// MARK: - Helper Methods

/// Format license key with dashes (XXXX-XXXX-XXXX-XXXX)
/// Preserves trial key format (TRIAL-timestamp-suffix)
private func formatLicenseKey(_ key: String) -> String {
    // Don't reformat trial keys - they have their own format
    if key.hasPrefix("TRIAL-") {
        return key
    }

    let cleaned = key.replacingOccurrences(of: "-", with: "")
    var formatted = ""

    for (index, character) in cleaned.enumerated() {
        if index > 0 && index % 4 == 0 {
            formatted += "-"
        }
        formatted += String(character)
    }

    return formatted
}

#Preview {
    LicenseSettingsView()
        .frame(width: 600, height: 500)
        .background(SnapbackTheme.Background.window)
}
