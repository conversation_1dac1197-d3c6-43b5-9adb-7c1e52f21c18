import SwiftUI

/// Modal view for showing license alerts with consistent styling
struct LicenseAlertView: View {
    let licenseStatus: LicenseStatus
    let onStartTrial: () -> Void
    let onPurchaseLicense: () -> Void
    let onEnterLicenseKey: () -> Void
    let onContinue: () -> Void
    let onDismiss: () -> Void

    var body: some View {
        VStack(spacing: SnapbackTheme.Padding.large) {
            // Header
            VStack(spacing: SnapbackTheme.Padding.standard) {
                Image(systemName: alertIcon)
                    .font(.system(size: 48))
                    .foregroundColor(SnapbackTheme.Text.accent)

                Text(alertTitle)
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(SnapbackTheme.Text.primary)

                Text(alertMessage)
                    .font(.body)
                    .foregroundColor(SnapbackTheme.Text.secondary)
                    .multilineTextAlignment(.center)
                    .lineLimit(nil)
            }

            // Action Buttons
            VStack(spacing: SnapbackTheme.Padding.small) {
                if licenseStatus == .unlicensed {
                    // Primary action for unlicensed users
                    Button("Start Free Trial") {
                        onStartTrial()
                    }
                    .buttonStyle(.borderedProminent)
                    .controlSize(.large)
                }

                // Secondary actions
                HStack(spacing: SnapbackTheme.Padding.standard) {
                    Button("Purchase License") {
                        onPurchaseLicense()
                    }
                    .buttonStyle(.bordered)

                    Button("Enter License Key") {
                        onEnterLicenseKey()
                    }
                    .buttonStyle(.bordered)
                }

                // Tertiary action
                Button("Continue") {
                    onContinue()
                }
                .buttonStyle(.plain)
                .foregroundColor(SnapbackTheme.Text.secondary)
                .font(.body)
            }
        }
        .padding(SnapbackTheme.Padding.large)
        .frame(width: 400)
        .background(SnapbackTheme.Background.card)
        .cornerRadius(12)
        .shadow(radius: 10)
    }

    // MARK: - Computed Properties

    private var alertIcon: String {
        switch licenseStatus {
        case .expired:
            return "clock.badge.exclamationmark.fill"
        case .invalid:
            return "exclamationmark.triangle.fill"
        case .unlicensed:
            return "key.fill"
        default:
            return "info.circle.fill"
        }
    }

    private var alertTitle: String {
        switch licenseStatus {
        case .expired:
            return "License Expired"
        case .invalid:
            return "Invalid License"
        case .unlicensed:
            return "License Required"
        default:
            return "License Verification"
        }
    }

    private var alertMessage: String {
        switch licenseStatus {
        case .expired:
            return "Your Snapback license has expired. To continue using all features, please purchase a new license or enter a valid license key."
        case .invalid:
            return "Your current license key is invalid. Please enter a valid license key or purchase a new license."
        case .unlicensed:
            return "Snapback requires a valid license to access all features. You can purchase a license, enter an existing license key, or start a free 15-day trial."
        default:
            return "To access all Snapback features, please purchase a license or enter your license key."
        }
    }
}

// MARK: - Preview

#Preview {
    struct PreviewWrapper: View {
        var body: some View {
            VStack(spacing: 20) {
                // Unlicensed state
                LicenseAlertView(
                    licenseStatus: .unlicensed,
                    onStartTrial: { print("Start Trial") },
                    onPurchaseLicense: { print("Purchase License") },
                    onEnterLicenseKey: { print("Enter License Key") },
                    onContinue: { print("Continue") },
                    onDismiss: { print("Dismiss") }
                )

                // Expired state
                LicenseAlertView(
                    licenseStatus: .expired,
                    onStartTrial: { print("Start Trial") },
                    onPurchaseLicense: { print("Purchase License") },
                    onEnterLicenseKey: { print("Enter License Key") },
                    onContinue: { print("Continue") },
                    onDismiss: { print("Dismiss") }
                )
            }
            .padding()
            .background(Color.gray.opacity(0.1))
        }
    }

    return PreviewWrapper()
}
