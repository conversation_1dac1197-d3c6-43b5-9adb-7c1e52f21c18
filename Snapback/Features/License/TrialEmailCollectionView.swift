import SwiftUI

/// Modal view for collecting user email when requesting a free trial
struct TrialEmailCollectionView: View {
    @StateObject private var licenseManager = LicenseManager.shared
    @State private var emailInput: String = ""
    @State private var isRequestingTrial: Bool = false
    @State private var showingSuccess: Bool = false
    @State private var errorMessage: String?

    let onDismiss: () -> Void
    let onSuccess: () -> Void

    var body: some View {
        VStack(spacing: SnapbackTheme.Padding.large) {
            // Header
            VStack(spacing: SnapbackTheme.Padding.standard) {
                Image(systemName: "gift.fill")
                    .font(.system(size: 48))
                    .foregroundColor(SnapbackTheme.Text.accent)

                Text("Start Your Free Trial")
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(SnapbackTheme.Text.primary)

                Text("Get 15 days of full access to all Snapback features")
                    .font(.body)
                    .foregroundColor(SnapbackTheme.Text.secondary)
                    .multilineTextAlignment(.center)
            }

            // Email Input Section
            VStack(alignment: .leading, spacing: SnapbackTheme.Padding.small) {
                Text("Email Address")
                    .font(.headline)
                    .foregroundColor(SnapbackTheme.Text.primary)

                TextField("<EMAIL>", text: $emailInput)
                    .textFieldStyle(.roundedBorder)
                    .disabled(isRequestingTrial)
                    .disableAutocorrection(true)
                    .textContentType(.emailAddress)
                    .onSubmit {
                        if isValidInput {
                            requestTrial()
                        }
                    }

                Text("We'll use this email to send you your trial license key")
                    .font(.caption)
                    .foregroundColor(SnapbackTheme.Text.secondary)
            }

            // Error Message
            if let errorMessage = errorMessage {
                HStack {
                    Image(systemName: "exclamationmark.triangle.fill")
                        .foregroundColor(.red)
                    Text(errorMessage)
                        .font(.caption)
                        .foregroundColor(.red)
                }
                .padding(.horizontal, SnapbackTheme.Padding.standard)
                .padding(.vertical, SnapbackTheme.Padding.small)
                .background(Color.red.opacity(0.1))
                .cornerRadius(8)
            }

            // Success Message
            if showingSuccess {
                HStack {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                    Text("Trial activated successfully!")
                        .font(.caption)
                        .foregroundColor(.green)
                }
                .padding(.horizontal, SnapbackTheme.Padding.standard)
                .padding(.vertical, SnapbackTheme.Padding.small)
                .background(Color.green.opacity(0.1))
                .cornerRadius(8)
            }

            // Action Buttons
            HStack(spacing: SnapbackTheme.Padding.standard) {
                // Cancel Button
                Button("Cancel") {
                    print("🔍 TRIAL MODAL DEBUG: Cancel button tapped")
                    onDismiss()
                }
                .buttonStyle(.bordered)
                .disabled(isRequestingTrial)

                // Start Trial Button
                Button(action: requestTrial) {
                    HStack {
                        if isRequestingTrial {
                            ProgressView()
                                .scaleEffect(0.8)
                                .progressViewStyle(CircularProgressViewStyle())
                        }
                        Text(isRequestingTrial ? "Requesting..." : "Start Free Trial")
                    }
                }
                .buttonStyle(.borderedProminent)
                .disabled(!isValidInput || isRequestingTrial)
            }

            // Terms and Privacy
            VStack(spacing: SnapbackTheme.Padding.small) {
                Text("By starting your trial, you agree to our Terms of Service and Privacy Policy")
                    .font(.caption2)
                    .foregroundColor(SnapbackTheme.Text.secondary)
                    .multilineTextAlignment(.center)
            }
        }
        .padding(SnapbackTheme.Padding.large)
        .frame(width: 400)
        .background(SnapbackTheme.Background.card)
        .cornerRadius(12)
        .shadow(radius: 10)
        .onChange(of: licenseManager.licenseStatus) { _, status in
            if status == .trial {
                showSuccess()
            }
        }
        .onChange(of: licenseManager.lastError) { _, error in
            if let error = error, isRequestingTrial {
                self.errorMessage = error
                self.isRequestingTrial = false
            }
        }
    }

    // MARK: - Computed Properties

    private var isValidInput: Bool {
        return !emailInput.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
            && isValidEmail(emailInput.trimmingCharacters(in: .whitespacesAndNewlines))
    }

    // MARK: - Methods

    private func requestTrial() {
        let trimmedEmail = emailInput.trimmingCharacters(in: .whitespacesAndNewlines).lowercased()

        guard isValidEmail(trimmedEmail) else {
            errorMessage = "Please enter a valid email address"
            return
        }

        // Clear any previous error
        errorMessage = nil
        isRequestingTrial = true

        Task {
            await licenseManager.requestTrialLicense(email: trimmedEmail)

            await MainActor.run {
                isRequestingTrial = false

                // Check if trial was successfully activated
                if licenseManager.licenseStatus == .trial {
                    showSuccess()
                } else if let error = licenseManager.lastError {
                    errorMessage = error
                }
            }
        }
    }

    private func showSuccess() {
        print("🔍 TRIAL MODAL DEBUG: showSuccess() called")
        showingSuccess = true

        // Auto-dismiss after showing success for a bit longer to let user see the message
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.5) {
            print("🔍 TRIAL MODAL DEBUG: Auto-closing modal after success")
            onSuccess()
        }
    }

    private func isValidEmail(_ email: String) -> Bool {
        let emailRegex = "^[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$"
        let emailPredicate = NSPredicate(format: "SELF MATCHES %@", emailRegex)
        return emailPredicate.evaluate(with: email)
    }
}

// MARK: - Preview

#if DEBUG
    struct TrialEmailCollectionView_Previews: PreviewProvider {
        static var previews: some View {
            TrialEmailCollectionView(
                onDismiss: { print("Dismissed") },
                onSuccess: { print("Success") }
            )
            .preferredColorScheme(.light)

            TrialEmailCollectionView(
                onDismiss: { print("Dismissed") },
                onSuccess: { print("Success") }
            )
            .preferredColorScheme(.dark)
        }
    }
#endif
