# License Settings Module

This module provides comprehensive license management functionality for the Snapback macOS app, following the established UI design patterns and architecture.

## Overview

The License settings module consists of:

- **LicenseManager**: Core service for license validation and storage
- **LicenseSettingsView**: SwiftUI interface following Snap<PERSON>'s design system
- **Integration**: Seamless integration with the existing settings architecture

## Features

### 🔑 License Management

- License key input with auto-formatting (XXXX-XXXX-XXXX-XXXX for regular keys)
- Trial license key support (TRIAL-timestamp-random format)
- Real-time validation with loading states
- Persistent storage using UserDefaults
- Error handling with user-friendly messages

### 🆓 Trial System

- 15-day free trial with full feature access
- Email collection modal for trial activation
- Automatic trial license key generation
- Trial status tracking with remaining days counter
- Seamless upgrade path from trial to paid license

### 📊 License Status Display

- Visual status indicators with appropriate colors
- Detailed license information (type, user, expiration)
- Feature list for valid licenses
- Expiration warnings for time-limited licenses

### 🎨 UI Design

- Follows SnapbackTheme design system
- Consistent with General tab styling
- Uses established form patterns and spacing
- Includes appropriate SF Symbols icons

### 🔧 Integration

- Seamlessly integrated into existing settings tabs
- Uses SettingsFooter for consistent footer styling
- Follows established logging patterns
- Compatible with existing UserDefaults structure

## License Key Formats

### Trial License Keys

Trial license keys are automatically generated when users request a free trial:

- Format: `TRIAL-{timestamp}-{random}`
- Example: `TRIAL-**********-5678`
- Duration: 15 days from activation
- Features: Full access to all Snapback features

### Regular License Keys

Regular license keys use the standard format:

- Format: `XXXX-XXXX-XXXX-XXXX` (4 groups of 4 characters)
- Example: `SNAP-BACK-2025-LIFE`
- Auto-formatted in UI with dashes for readability

## Demo/Test License Keys

For testing purposes, the following demo license keys are available:

| License Key (Input) | Formatted Display     | Type             | Status           | Features                                                                                           |
| ------------------- | --------------------- | ---------------- | ---------------- | -------------------------------------------------------------------------------------------------- |
| `snapback2025life`  | `SNAP-BACK-2025-LIFE` | Lifetime License | Valid (Lifetime) | All Workspace Features, Unlimited Workspaces, Window Management, Keyboard Shortcuts, CloudKit Sync |
| `snapback2025year`  | `SNAP-BACK-2025-YEAR` | Annual License   | Valid (Expiring) | All Workspace Features, Unlimited Workspaces, Window Management, Keyboard Shortcuts, CloudKit Sync |

**Note**: Keys can be entered with or without dashes. The system automatically formats them for display and cleans them for validation.

All other keys will be marked as invalid.

## Architecture

### LicenseManager

```swift
class LicenseManager: ObservableObject {
    // Published properties for UI binding
    @Published var licenseStatus: LicenseStatus
    @Published var licenseKey: String
    @Published var userEmail: String
    @Published var licenseInfo: LicenseInfo?
    @Published var isTrialActive: Bool
    @Published var remainingTrialDays: Int

    // Core methods
    func validateLicense(silent: Bool = false) async
    func setLicenseKey(_ key: String) async
    func setLicenseKeyAndEmail(_ key: String, email: String) async
    func requestTrialLicense(email: String) async
    func clearLicense()

    // Computed properties
    var hasFullAccess: Bool // true for valid licenses and active trials
}
```

### LicenseStatus

```swift
enum LicenseStatus: String, CaseIterable {
    case unlicensed = "unlicensed"
    case trial = "trial"
    case valid = "valid"
    case invalid = "invalid"
    case expired = "expired"
}
```

### LicenseInfo

```swift
struct LicenseInfo: Codable {
    let licenseType: String
    let registeredUser: String
    let email: String?
    let expirationDate: Date?
    let features: [String]
}
```

### LicenseAPIService

The mock API service handles license validation and trial requests:

```swift
class LicenseAPIService {
    // Trial license request
    func requestTrialLicense(email: String) async throws -> TrialResponse

    // License validation
    func validateLicense(licenseKey: String, email: String) async throws -> ValidationResponse
}
```

**Mock API Behavior:**

- Trial requests generate `TRIAL-{timestamp}-{random}` keys
- Validation supports both formatted (`SNAP-BACK-2025-LIFE`) and unformatted (`snapback2025life`) keys
- Network delay simulation (0.5 seconds) for realistic UX testing

## Usage

### Adding to Settings

The License tab is automatically included in the settings interface:

```swift
// In SettingsView.swift
tabList.append(Tab(id: "license", title: "License", icon: "key"))

// In content switch
case "license":
    LicenseSettingsView()
```

### Accessing License Manager

```swift
// Get shared instance
let licenseManager = LicenseManager.shared

// Check license status
if licenseManager.hasFullAccess {
    // User has full access (valid license or active trial)
}

// Request a trial license
await licenseManager.requestTrialLicense(email: "<EMAIL>")

// Set license key with email (recommended)
await licenseManager.setLicenseKeyAndEmail("snapback2025life", email: "<EMAIL>")

// Set license key only (legacy support)
await licenseManager.setLicenseKey("SNAP-BACK-2025-LIFE")

// Check trial status
if licenseManager.isTrialActive {
    print("Trial active with \(licenseManager.remainingTrialDays) days remaining")
}

// Clear license (removes both license and trial data)
licenseManager.clearLicense()
```

## Styling Consistency

The module follows Snapback's established design patterns:

### Theme Usage

- `SnapbackTheme.Background.card` for GroupBox backgrounds
- `SnapbackTheme.Text.error` for error messages
- `SnapbackTheme.Padding.section` for consistent spacing
- `snapbackSectionTitleStyle()` for section headers
- `snapbackRowStyle()` for form rows

### Icons

- `key` for the License tab icon
- `checkmark.circle.fill` for valid status
- `xmark.circle.fill` for invalid status
- `clock` for trial status
- `clock.badge.exclamationmark` for expired status
- `exclamationmark.triangle` for unlicensed status

### Layout

- Follows the same ScrollView + VStack pattern as General settings
- Uses GroupBox for section containers
- Implements SettingsFooter for consistent footer styling
- Maintains proper spacing and visual hierarchy

## Logging

The module integrates with Snapback's logging system:

```swift
private let logger = LoggingService.shared
private let serviceName = "LicenseManager"

// Example logging
logger.info("License validation successful", service: serviceName)
logger.warning("License validation failed: invalid key", service: serviceName)
logger.error("License validation error: \(error)", service: serviceName)
```

## Persistence

License information is stored using UserDefaults with consistent key naming:

### License Data

- `SnapbackLicenseKey`: The license key (formatted for display)
- `SnapbackUserEmail`: User's email address
- `SnapbackLicenseStatus`: Current license status
- `SnapbackLicenseInfo`: Encoded license information
- `SnapbackLastLicenseValidation`: Timestamp of last validation

### Trial Data

- `SnapbackTrialStartDate`: Trial activation date
- `SnapbackTrialEndDate`: Trial expiration date
- `SnapbackTrialChecksum`: Data integrity verification
- `SnapbackFirstLaunchDate`: First app launch timestamp

## Future Enhancements

Potential improvements for production use:

1. **Real API Integration**: Replace placeholder validation with actual license server
2. **Offline Validation**: Add cryptographic license validation for offline use
3. **Auto-Renewal**: Implement automatic license renewal for subscription licenses
4. **License Transfer**: Add functionality to transfer licenses between devices
5. **Usage Analytics**: Track feature usage based on license type
6. **Grace Period**: Implement grace period for expired licenses

## Testing

### License Key Testing

To test the license functionality:

1. Open Snapback Settings
2. Navigate to the License tab
3. Enter demo license keys (with or without dashes):
   - `snapback2025life` or `SNAP-BACK-2025-LIFE`
   - `snapback2025year` or `SNAP-BACK-2025-YEAR`
4. Observe the validation process and status updates
5. Test different license types and statuses
6. Verify persistence by restarting the app

### Trial System Testing

To test the trial functionality:

1. Start with an unlicensed state (clear any existing license)
2. Click "Start Free Trial" from the license alert or menu
3. Enter a valid email address in the trial collection modal
4. Test the cancel button functionality
5. Complete trial activation and observe:
   - Success message display
   - Automatic modal closure after 2.5 seconds
   - Trial license key generation (`TRIAL-{timestamp}-{random}`)
   - Trial status display with remaining days
   - License settings UI updates with trial data
6. Restart the app to verify trial persistence
7. Test upgrading from trial to paid license
8. Test clearing trial data with "Clear License"

### UI Testing Checklist

- [ ] Cancel button closes modal immediately
- [ ] Success message shows for 2.5 seconds before auto-close
- [ ] Trial license keys display without reformatting
- [ ] Regular license keys auto-format with dashes
- [ ] License settings UI updates reactively
- [ ] Trial status shows correct remaining days
- [ ] Clear license removes all data (license + trial)

The module provides comprehensive license management while maintaining consistency with Snapback's existing design and architecture patterns.
