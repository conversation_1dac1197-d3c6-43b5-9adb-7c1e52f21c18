import Foundation
import SwiftUI

/// Manages license validation and storage for the Snapback app
class LicenseManager: ObservableObject {
    static let shared = LicenseManager()

    /// Published properties for UI binding
    @Published var licenseStatus: LicenseStatus = .unlicensed
    @Published var licenseKey: String = ""
    @Published var userEmail: String = ""
    @Published var licenseInfo: LicenseInfo?
    @Published var isValidating: Bool = false
    @Published var lastError: String?

    /// Trial-related properties
    @Published var trialStartDate: Date?
    @Published var trialEndDate: Date?
    @Published var remainingTrialDays: Int = 0
    @Published var isTrialActive: Bool = false

    /// Logger for license-related events
    private let logger = LoggingService.shared
    private let serviceName = "LicenseManager"

    /// UserDefaults keys for persistence
    private enum PersistenceKeys {
        static let licenseKey = "SnapbackLicenseKey"
        static let userEmail = "SnapbackUserEmail"
        static let licenseStatus = "SnapbackLicenseStatus"
        static let licenseInfo = "SnapbackLicenseInfo"
        static let lastValidation = "SnapbackLastLicenseValidation"

        // Trial-related keys
        static let trialStartDate = "SnapbackTrialStartDate"
        static let trialEndDate = "SnapbackTrialEndDate"
        static let firstLaunchDate = "SnapbackFirstLaunchDate"
        static let trialChecksum = "SnapbackTrialChecksum"
    }

    /// License validation endpoint (placeholder - replace with actual endpoint)
    private let validationEndpoint = "https://api.snapback.app/license/validate"

    /// Trial configuration
    private let trialDurationDays = 15

    /// Computed property for access control
    var hasFullAccess: Bool {
        return licenseStatus == .valid || licenseStatus == .trial
    }

    private init() {
        logger.info("Initializing LicenseManager", service: serviceName)
        loadPersistedLicense()

        // Note: Automatic trial initialization removed - users must explicitly request trials
        // initializeTrialIfNeeded() // DISABLED: No automatic trial activation

        // Update trial status (for existing trials only)
        updateTrialStatus()

        // Validate license on startup if we have one
        if !licenseKey.isEmpty {
            Task {
                await validateLicense(silent: true)
            }
        }
    }

    // MARK: - Public Methods

    /// Validate the current license key
    @MainActor
    func validateLicense(silent: Bool = false) async {
        guard !licenseKey.isEmpty else {
            if !silent {
                lastError = "Please enter a license key"
            }
            return
        }

        isValidating = true
        lastError = nil

        logger.info("Validating license key: \(licenseKey.prefix(8))...", service: serviceName)

        do {
            // Use API service if we have both license key and email
            if !userEmail.isEmpty && isValidEmail(userEmail) {
                logger.info("Using API validation with email: \(userEmail)", service: serviceName)
                // Clean the license key for API validation (remove dashes)
                let cleanedKey = licenseKey.replacingOccurrences(of: "-", with: "")
                let response = try await LicenseAPIService.shared.validateLicense(
                    licenseKey: cleanedKey, email: userEmail)

                if response.valid {
                    licenseStatus = .valid

                    // Create license info from API response
                    licenseInfo = LicenseInfo(
                        licenseType: response.licenseType ?? "License",
                        registeredUser: response.registeredUser ?? userEmail,
                        email: response.email ?? userEmail,
                        expirationDate: {
                            if let expirationString = response.expirationDate {
                                return ISO8601DateFormatter().date(from: expirationString)
                            }
                            return nil
                        }(),
                        features: response.features ?? ["All Features"]
                    )

                    logger.info("API license validation successful", service: serviceName)
                } else {
                    licenseStatus = .invalid
                    licenseInfo = nil
                    lastError = response.error ?? "Invalid license key"
                    logger.warning(
                        "API license validation failed: \(lastError ?? "unknown")",
                        service: serviceName)
                }
            } else {
                // Fall back to local validation for backward compatibility
                logger.info("Using local validation (no email provided)", service: serviceName)
                let result = try await performLicenseValidation(licenseKey)

                switch result.status {
                case .valid:
                    licenseStatus = .valid
                    licenseInfo = result.info
                    logger.info("Local license validation successful", service: serviceName)

                case .invalid:
                    licenseStatus = .invalid
                    licenseInfo = nil
                    lastError = result.message ?? "Invalid license key"
                    logger.warning(
                        "Local license validation failed: invalid key", service: serviceName)

                case .expired:
                    licenseStatus = .expired
                    licenseInfo = result.info
                    lastError = result.message ?? "License has expired"
                    logger.warning("Local license validation failed: expired", service: serviceName)

                case .unlicensed:
                    licenseStatus = .unlicensed
                    licenseInfo = nil
                    lastError = result.message ?? "No license found"
                    logger.info("No license found", service: serviceName)

                case .trial:
                    // This shouldn't happen during license validation, but handle it
                    logger.warning(
                        "Unexpected trial status during license validation", service: serviceName)
                }
            }

            // Persist the results
            persistLicense()

            // Notify about license status change
            postLicenseStatusChangeNotification()

        } catch {
            licenseStatus = .invalid
            licenseInfo = nil
            lastError = "Validation failed: \(error.localizedDescription)"
            logger.error("License validation error: \(error)", service: serviceName)
        }

        isValidating = false
    }

    /// Set a new license key and validate it
    @MainActor
    func setLicenseKey(_ key: String) async {
        let cleanKey = key.trimmingCharacters(in: .whitespacesAndNewlines)
        licenseKey = formatLicenseKeyForDisplay(cleanKey)

        if !cleanKey.isEmpty {
            await validateLicense()
        } else {
            licenseStatus = .unlicensed
            licenseInfo = nil
            lastError = nil
            persistLicense()
        }
    }

    /// Set and validate a license key with email
    @MainActor
    func setLicenseKeyAndEmail(_ key: String, email: String) async {
        let trimmedKey = key.trimmingCharacters(in: .whitespacesAndNewlines)
        let trimmedEmail = email.trimmingCharacters(in: .whitespacesAndNewlines).lowercased()

        logger.info(
            "Setting license key and email: \(trimmedKey.prefix(8))... for \(trimmedEmail)",
            service: serviceName)

        // Validate email format
        guard isValidEmail(trimmedEmail) else {
            lastError = "Please enter a valid email address"
            return
        }

        // Clear any previous error
        lastError = nil

        // Update the key and email (format the key for display)
        licenseKey = formatLicenseKeyForDisplay(trimmedKey)
        userEmail = trimmedEmail

        if !trimmedKey.isEmpty {
            await validateLicense()
        } else {
            licenseStatus = .unlicensed
            licenseInfo = nil
            lastError = nil
            persistLicense()
        }
    }

    /// Clear the current license
    func clearLicense() {
        logger.info("Clearing license", service: serviceName)

        // Clear license-related properties
        licenseKey = ""
        userEmail = ""
        licenseStatus = .unlicensed
        licenseInfo = nil
        lastError = nil

        // Clear trial-related properties
        trialStartDate = nil
        trialEndDate = nil
        isTrialActive = false
        remainingTrialDays = 0

        // Clear from UserDefaults - both license and trial data
        UserDefaults.standard.removeObject(forKey: PersistenceKeys.licenseKey)
        UserDefaults.standard.removeObject(forKey: PersistenceKeys.userEmail)
        UserDefaults.standard.removeObject(forKey: PersistenceKeys.licenseStatus)
        UserDefaults.standard.removeObject(forKey: PersistenceKeys.licenseInfo)
        UserDefaults.standard.removeObject(forKey: PersistenceKeys.lastValidation)

        // Clear trial-related UserDefaults
        UserDefaults.standard.removeObject(forKey: PersistenceKeys.trialStartDate)
        UserDefaults.standard.removeObject(forKey: PersistenceKeys.trialEndDate)
        UserDefaults.standard.removeObject(forKey: PersistenceKeys.trialChecksum)
        UserDefaults.standard.removeObject(forKey: PersistenceKeys.firstLaunchDate)
        UserDefaults.standard.synchronize()

        logger.info(
            "🔍 LICENSE CLEAR DEBUG: All license and trial data cleared", service: serviceName)

        // Notify about license status change
        postLicenseStatusChangeNotification()
    }

    // MARK: - Trial Management

    /// Initialize trial if this is the first launch and no license exists
    private func initializeTrialIfNeeded() {
        // For testing purposes, always start a fresh trial
        // This creates an active 15-day trial account for testing
        if licenseStatus == .unlicensed && licenseKey.isEmpty {
            // Clear any existing trial data to start fresh
            UserDefaults.standard.removeObject(forKey: PersistenceKeys.firstLaunchDate)
            UserDefaults.standard.removeObject(forKey: PersistenceKeys.trialStartDate)
            UserDefaults.standard.removeObject(forKey: PersistenceKeys.trialEndDate)
            UserDefaults.standard.removeObject(forKey: PersistenceKeys.trialChecksum)

            // Record first launch as now
            let now = Date()
            UserDefaults.standard.set(now, forKey: PersistenceKeys.firstLaunchDate)
            logger.info("Testing trial initialized, recorded date: \(now)", service: serviceName)

            // Start fresh trial
            startTrial()
        }
    }

    /// Request a trial license from the API using the provided email
    @MainActor
    func requestTrialLicense(email: String) async {
        logger.info("Requesting trial license for email: \(email)", service: serviceName)

        // Validate email format
        guard isValidEmail(email) else {
            lastError = "Please enter a valid email address"
            return
        }

        // Clear any previous error and set loading state
        lastError = nil
        isValidating = true

        do {
            // Request trial license from API
            let response = try await LicenseAPIService.shared.requestTrialLicense(email: email)

            if response.success, let licenseKey = response.licenseKey {
                // Store the email and license key (format for display)
                userEmail = email
                self.licenseKey = formatLicenseKeyForDisplay(licenseKey)

                // Parse expiration date
                var expirationDate: Date?
                if let expirationString = response.expirationDate {
                    let formatter = ISO8601DateFormatter()
                    expirationDate = formatter.date(from: expirationString)
                }

                // Set up trial state
                let now = Date()
                trialStartDate = now
                trialEndDate =
                    expirationDate ?? Calendar.current.date(
                        byAdding: .day, value: trialDurationDays, to: now)!
                licenseStatus = .trial
                isTrialActive = true

                // Create license info for trial
                licenseInfo = LicenseInfo(
                    licenseType: "Trial",
                    registeredUser: email,
                    email: email,
                    expirationDate: trialEndDate,
                    features: [
                        "All Workspace Features", "Unlimited Workspaces",
                        "Window Management", "Keyboard Shortcuts", "CloudKit Sync",
                    ]
                )

                // Generate checksum for trial data integrity
                let checksum = generateTrialChecksum(startDate: now, endDate: trialEndDate!)
                UserDefaults.standard.set(checksum, forKey: PersistenceKeys.trialChecksum)

                persistTrialData()
                persistLicense()

                // Update trial status to calculate remaining days
                updateTrialStatus()

                // DIAGNOSTIC: Log trial activation details
                logger.info(
                    "🔍 TRIAL ACTIVATION DEBUG: Trial license activated successfully for \(email)",
                    service: serviceName)
                logger.info(
                    "🔍 TRIAL ACTIVATION DEBUG: License key: \(licenseKey)", service: serviceName)
                logger.info(
                    "🔍 TRIAL ACTIVATION DEBUG: Trial start: \(trialStartDate?.description ?? "nil")",
                    service: serviceName)
                logger.info(
                    "🔍 TRIAL ACTIVATION DEBUG: Trial end: \(trialEndDate?.description ?? "nil")",
                    service: serviceName)
                logger.info(
                    "🔍 TRIAL ACTIVATION DEBUG: License status: \(licenseStatus)",
                    service: serviceName)

                // Notify about license status change
                postLicenseStatusChangeNotification()

            } else {
                // Handle API error
                lastError = response.error ?? "Failed to request trial license"
                logger.error(
                    "Trial request failed: \(lastError ?? "Unknown error")", service: serviceName)
            }

        } catch {
            lastError = "Network error: \(error.localizedDescription)"
            logger.error("Trial request network error: \(error)", service: serviceName)
        }

        isValidating = false
    }

    /// Legacy method for backward compatibility - now redirects to API-based flow
    func startTrial() {
        logger.warning(
            "startTrial() called - this method is deprecated. Use requestTrialLicense(email:) instead.",
            service: serviceName)

        // For backward compatibility, start a basic trial without email
        // This should only be used in emergency cases
        let now = Date()
        let endDate =
            Calendar.current.date(byAdding: .day, value: trialDurationDays, to: now) ?? now

        trialStartDate = now
        trialEndDate = endDate
        licenseStatus = .trial
        isTrialActive = true

        // Create basic license info for trial
        licenseInfo = LicenseInfo(
            licenseType: "Trial",
            registeredUser: "Trial User",
            email: "<EMAIL>",
            expirationDate: endDate,
            features: [
                "All Workspace Features", "Unlimited Workspaces",
                "Window Management", "Keyboard Shortcuts", "CloudKit Sync",
            ]
        )

        // Generate checksum for trial data integrity
        let checksum = generateTrialChecksum(startDate: now, endDate: endDate)
        UserDefaults.standard.set(checksum, forKey: PersistenceKeys.trialChecksum)

        persistTrialData()
        persistLicense()
        postLicenseStatusChangeNotification()

        logger.info("Legacy trial started: \(trialDurationDays) days", service: serviceName)
    }

    /// Update trial status and remaining days
    private func updateTrialStatus() {
        guard let startDate = trialStartDate,
            let endDate = trialEndDate
        else {
            isTrialActive = false
            remainingTrialDays = 0
            return
        }

        // Verify trial data integrity
        if !verifyTrialIntegrity(startDate: startDate, endDate: endDate) {
            logger.warning(
                "Trial data integrity check failed - resetting trial", service: serviceName)
            resetTrialData()
            return
        }

        let now = Date()

        if now <= endDate {
            // Trial is still active
            isTrialActive = true
            let calendar = Calendar.current
            let components = calendar.dateComponents([.day], from: now, to: endDate)
            let calculatedDays = components.day ?? 0

            // Ensure we always show at least 1 day if trial is still active (same day)
            // This handles the edge case where trial expires later today
            if calculatedDays == 0 && now < endDate {
                remainingTrialDays = 1
                logger.info(
                    "🔍 TRIAL DAYS DEBUG: Same-day trial, showing 1 day remaining",
                    service: serviceName)
            } else {
                remainingTrialDays = max(0, calculatedDays)
            }

            // DIAGNOSTIC: Log detailed trial calculation info
            logger.info("🔍 TRIAL DAYS DEBUG: Trial is active", service: serviceName)
            logger.info("🔍 TRIAL DAYS DEBUG: Current date: \(now)", service: serviceName)
            logger.info("🔍 TRIAL DAYS DEBUG: End date: \(endDate)", service: serviceName)
            logger.info("🔍 TRIAL DAYS DEBUG: Date components: \(components)", service: serviceName)
            logger.info(
                "🔍 TRIAL DAYS DEBUG: Calculated remaining days: \(remainingTrialDays)",
                service: serviceName)

            if licenseStatus != .valid {
                licenseStatus = .trial
                // Ensure trial license info is set
                if licenseInfo == nil {
                    licenseInfo = LicenseInfo(
                        licenseType: "Trial",
                        registeredUser: "Trial User",
                        email: "<EMAIL>",  // Placeholder - will be updated with real email
                        expirationDate: endDate,
                        features: [
                            "All Workspace Features", "Unlimited Workspaces",
                            "Window Management", "Keyboard Shortcuts", "CloudKit Sync",
                        ]
                    )
                }
            }
        } else {
            // Trial has expired
            isTrialActive = false
            remainingTrialDays = 0

            // DIAGNOSTIC: Log expired trial info
            logger.info("🔍 TRIAL DAYS DEBUG: Trial has expired", service: serviceName)
            logger.info("🔍 TRIAL DAYS DEBUG: Current date: \(now)", service: serviceName)
            logger.info("🔍 TRIAL DAYS DEBUG: End date: \(endDate)", service: serviceName)
            logger.info(
                "🔍 TRIAL DAYS DEBUG: Time difference: \(now.timeIntervalSince(endDate)) seconds",
                service: serviceName)

            if licenseStatus == .trial {
                licenseStatus = .expired
                // Update license info to reflect expired status
                licenseInfo = LicenseInfo(
                    licenseType: "Expired Trial",
                    registeredUser: "Trial User",
                    email: "<EMAIL>",  // Placeholder - will be updated with real email
                    expirationDate: endDate,
                    features: ["Trial Expired"]
                )
                logger.info("Trial expired on \(endDate)", service: serviceName)

                // Notify about trial expiration
                postLicenseStatusChangeNotification()
            }
        }
    }

    /// Generate checksum for trial data integrity
    private func generateTrialChecksum(startDate: Date, endDate: Date) -> String {
        let data =
            "\(startDate.timeIntervalSince1970):\(endDate.timeIntervalSince1970):\(trialDurationDays)"
        return String(data.hashValue)
    }

    /// Validate email format
    private func isValidEmail(_ email: String) -> Bool {
        let emailRegex = "^[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$"
        let emailPredicate = NSPredicate(format: "SELF MATCHES %@", emailRegex)
        return emailPredicate.evaluate(with: email)
    }

    /// Verify trial data integrity
    private func verifyTrialIntegrity(startDate: Date, endDate: Date) -> Bool {
        guard
            let storedChecksum = UserDefaults.standard.string(forKey: PersistenceKeys.trialChecksum)
        else {
            return false
        }

        let expectedChecksum = generateTrialChecksum(startDate: startDate, endDate: endDate)
        return storedChecksum == expectedChecksum
    }

    /// Reset trial data (for debugging or integrity failures)
    func resetTrialData() {
        trialStartDate = nil
        trialEndDate = nil
        isTrialActive = false
        remainingTrialDays = 0

        UserDefaults.standard.removeObject(forKey: PersistenceKeys.trialStartDate)
        UserDefaults.standard.removeObject(forKey: PersistenceKeys.trialEndDate)
        UserDefaults.standard.removeObject(forKey: PersistenceKeys.trialChecksum)
        UserDefaults.standard.synchronize()
    }

    /// Persist trial data to UserDefaults
    private func persistTrialData() {
        if let startDate = trialStartDate {
            UserDefaults.standard.set(startDate, forKey: PersistenceKeys.trialStartDate)
        }
        if let endDate = trialEndDate {
            UserDefaults.standard.set(endDate, forKey: PersistenceKeys.trialEndDate)
        }
        UserDefaults.standard.synchronize()
    }

    /// Post notification about license status change
    private func postLicenseStatusChangeNotification() {
        NotificationCenter.default.post(
            name: NSNotification.Name("LicenseStatusChanged"),
            object: licenseStatus
        )
    }

    // MARK: - Private Methods

    /// Format license key for display
    /// Preserves trial key format (TRIAL-timestamp-suffix)
    /// Formats regular keys as XXXX-XXXX-XXXX-XXXX
    private func formatLicenseKeyForDisplay(_ key: String) -> String {
        // Don't reformat trial keys - they have their own format
        if key.hasPrefix("TRIAL-") {
            return key
        }

        // Remove existing dashes and convert to uppercase
        let cleaned = key.replacingOccurrences(of: "-", with: "").uppercased()

        // Format as XXXX-XXXX-XXXX-XXXX for regular keys
        var formatted = ""
        for (index, character) in cleaned.enumerated() {
            if index > 0 && index % 4 == 0 {
                formatted += "-"
            }
            formatted += String(character)
        }

        return formatted
    }

    /// Load persisted license information
    private func loadPersistedLicense() {
        licenseKey = UserDefaults.standard.string(forKey: PersistenceKeys.licenseKey) ?? ""
        userEmail = UserDefaults.standard.string(forKey: PersistenceKeys.userEmail) ?? ""

        if let statusRaw = UserDefaults.standard.string(forKey: PersistenceKeys.licenseStatus),
            let status = LicenseStatus(rawValue: statusRaw)
        {
            licenseStatus = status
        }

        if let infoData = UserDefaults.standard.data(forKey: PersistenceKeys.licenseInfo),
            let info = try? JSONDecoder().decode(LicenseInfo.self, from: infoData)
        {
            licenseInfo = info
        }

        // Load trial data
        trialStartDate =
            UserDefaults.standard.object(forKey: PersistenceKeys.trialStartDate) as? Date
        trialEndDate = UserDefaults.standard.object(forKey: PersistenceKeys.trialEndDate) as? Date

        // DIAGNOSTIC: Log detailed license loading info
        logger.info(
            "🔍 LICENSE LOAD DEBUG: Loaded persisted license - Status: \(licenseStatus)",
            service: serviceName)
        logger.info(
            "🔍 LICENSE LOAD DEBUG: License key: '\(licenseKey.isEmpty ? "EMPTY" : "SET (\(licenseKey.count) chars)")'",
            service: serviceName)
        logger.info(
            "🔍 LICENSE LOAD DEBUG: User email: '\(userEmail.isEmpty ? "EMPTY" : userEmail)'",
            service: serviceName)
        logger.info(
            "🔍 LICENSE LOAD DEBUG: Trial start: \(trialStartDate?.description ?? "nil")",
            service: serviceName)
        logger.info(
            "🔍 LICENSE LOAD DEBUG: Trial end: \(trialEndDate?.description ?? "nil")",
            service: serviceName)
    }

    /// Persist license information to UserDefaults
    private func persistLicense() {
        UserDefaults.standard.set(licenseKey, forKey: PersistenceKeys.licenseKey)
        UserDefaults.standard.set(userEmail, forKey: PersistenceKeys.userEmail)
        UserDefaults.standard.set(licenseStatus.rawValue, forKey: PersistenceKeys.licenseStatus)
        UserDefaults.standard.set(
            Date().timeIntervalSince1970, forKey: PersistenceKeys.lastValidation)

        if let info = licenseInfo,
            let infoData = try? JSONEncoder().encode(info)
        {
            UserDefaults.standard.set(infoData, forKey: PersistenceKeys.licenseInfo)
        } else {
            UserDefaults.standard.removeObject(forKey: PersistenceKeys.licenseInfo)
        }

        UserDefaults.standard.synchronize()

        // DIAGNOSTIC: Log license persistence
        logger.info("🔍 LICENSE PERSIST DEBUG: License information persisted", service: serviceName)
        logger.info("🔍 LICENSE PERSIST DEBUG: Status: \(licenseStatus)", service: serviceName)
        logger.info(
            "🔍 LICENSE PERSIST DEBUG: Key: '\(licenseKey.isEmpty ? "EMPTY" : "SET")'",
            service: serviceName)
        logger.info(
            "🔍 LICENSE PERSIST DEBUG: Email: '\(userEmail.isEmpty ? "EMPTY" : userEmail)'",
            service: serviceName)
    }

    /// Perform the actual license validation (placeholder implementation)
    private func performLicenseValidation(_ key: String) async throws -> LicenseValidationResult {
        // Simulate network delay
        try await Task.sleep(nanoseconds: 1_000_000_000)  // 1 second

        // Clean the key for comparison
        let cleanKey = key.replacingOccurrences(of: "-", with: "").lowercased()

        // Demo license keys for testing different license types
        switch cleanKey {
        case "snapback2025life":
            // Lifetime license - never expires
            return LicenseValidationResult(
                status: .valid,
                info: LicenseInfo(
                    licenseType: "Lifetime License",
                    registeredUser: "Licensed User",
                    email: "<EMAIL>",  // Placeholder - will be updated with real email
                    expirationDate: nil,  // Lifetime license
                    features: [
                        "All Workspace Features", "Unlimited Workspaces",
                        "Window Management", "Keyboard Shortcuts", "CloudKit Sync",
                    ]
                ),
                message: "Lifetime license activated successfully"
            )

        case "snapback2025year":
            // Expiring license - valid for 1 year from now
            let expirationDate = Calendar.current.date(byAdding: .year, value: 1, to: Date())
            return LicenseValidationResult(
                status: .valid,
                info: LicenseInfo(
                    licenseType: "Annual License",
                    registeredUser: "Licensed User",
                    email: "<EMAIL>",  // Placeholder - will be updated with real email
                    expirationDate: expirationDate,
                    features: [
                        "All Workspace Features", "Unlimited Workspaces",
                        "Window Management", "Keyboard Shortcuts", "CloudKit Sync",
                    ]
                ),
                message: "Annual license activated successfully"
            )

        case "testexpiredlic":
            // Demo key that simulates an expired paid license for testing
            return LicenseValidationResult(
                status: .expired,
                info: LicenseInfo(
                    licenseType: "Expired Annual License",
                    registeredUser: "Test User",
                    email: "<EMAIL>",  // Placeholder - will be updated with real email
                    expirationDate: Calendar.current.date(byAdding: .day, value: -30, to: Date()),  // 30 days ago
                    features: ["License Expired"]
                ),
                message: "License has expired"
            )

        case "testexprtrial123":
            // Demo key that simulates an expired trial for testing
            return LicenseValidationResult(
                status: .expired,
                info: LicenseInfo(
                    licenseType: "Expired Trial",
                    registeredUser: "Test User",
                    email: "<EMAIL>",  // Placeholder - will be updated with real email
                    expirationDate: Calendar.current.date(byAdding: .day, value: -1, to: Date()),  // Yesterday
                    features: ["Trial Expired"]
                ),
                message: "Trial period has expired"
            )

        case "test2025expr":
            // Standard format expired license test key: TEST-2025-EXPR-IRED
            return LicenseValidationResult(
                status: .expired,
                info: LicenseInfo(
                    licenseType: "Expired Annual License",
                    registeredUser: "Test User",
                    email: "<EMAIL>",  // Placeholder - will be updated with real email
                    expirationDate: Calendar.current.date(byAdding: .day, value: -30, to: Date()),  // 30 days ago
                    features: ["License Expired"]
                ),
                message:
                    "Your license has expired. Please purchase a new license or enter a valid license key."
            )

        default:
            // Invalid license key
            return LicenseValidationResult(
                status: .invalid,
                info: nil,
                message: "Invalid license key. Please check your key and try again."
            )
        }
    }
}

// MARK: - Supporting Types

/// License status enumeration
enum LicenseStatus: String, CaseIterable {
    case unlicensed = "unlicensed"
    case trial = "trial"
    case valid = "valid"
    case invalid = "invalid"
    case expired = "expired"

    var displayName: String {
        switch self {
        case .unlicensed: return "No License"
        case .trial: return "Trial Active"
        case .valid: return "Valid"
        case .invalid: return "Invalid"
        case .expired: return "Expired"
        }
    }

    var color: Color {
        switch self {
        case .unlicensed: return .secondary
        case .trial: return .blue
        case .valid: return .green
        case .invalid: return .red
        case .expired: return .orange
        }
    }
}

/// License information structure
struct LicenseInfo: Codable {
    let licenseType: String
    let registeredUser: String
    let email: String
    let expirationDate: Date?
    let features: [String]

    var isExpired: Bool {
        guard let expirationDate = expirationDate else { return false }
        return expirationDate < Date()
    }

    var expirationDisplayText: String {
        guard let expirationDate = expirationDate else { return "Never expires" }

        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .none

        return formatter.string(from: expirationDate)
    }
}

/// License validation result
struct LicenseValidationResult {
    let status: LicenseStatus
    let info: LicenseInfo?
    let message: String?
}
