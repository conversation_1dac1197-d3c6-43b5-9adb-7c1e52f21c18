import Cocoa
import SwiftUI

/// Window controller for the trial email collection modal
class TrialEmailWindowController: NSWindowController, NSWindowDelegate {
    private var onCompletion: (() -> Void)?

    convenience init(onCompletion: @escaping () -> Void) {
        // Create the window - Native alert sizing
        let window = NSWindow(
            contentRect: NSRect(x: 0, y: 0, width: 440, height: 320),
            styleMask: [.titled, .closable],
            backing: .buffered,
            defer: false
        )

        // Configure window
        window.title = "Snapback"
        window.isReleasedWhenClosed = false
        window.center()
        window.level = .modalPanel
        window.isMovableByWindowBackground = true

        // Initialize with the window
        self.init(window: window)
        self.onCompletion = onCompletion

        // Create the SwiftUI view
        let trialView = TrialEmailCollectionView(
            onDismiss: { [weak self] in
                print("🔍 TRIAL MODAL DEBUG: onDismiss callback called")
                self?.closeModal()
            },
            onSuccess: { [weak self] in
                print("🔍 TRIAL MODAL DEBUG: onSuccess callback called")
                self?.closeModal()
            }
        )

        // Set up the hosting view
        let hostingView = NSHostingView(rootView: trialView)
        window.contentView = hostingView

        // Make window non-resizable
        window.styleMask.remove(.resizable)

        // Set minimum and maximum size to prevent resizing
        window.minSize = NSSize(width: 440, height: 320)
        window.maxSize = NSSize(width: 440, height: 320)

        // Set window delegate to receive close notifications
        window.delegate = self
    }

    /// Show the modal window
    func showModal() {
        guard let window = window else { return }

        // Center the window
        window.center()

        // Show the window
        window.makeKeyAndOrderFront(nil)

        // Bring to front
        NSApp.activate(ignoringOtherApps: true)
    }

    /// Close the modal window
    private func closeModal() {
        print("🔍 TRIAL MODAL DEBUG: closeModal() called")
        window?.close()
        onCompletion?()
        print("🔍 TRIAL MODAL DEBUG: Modal window closed and completion called")
    }

    /// Handle window closing
    func windowWillClose(_ notification: Notification) {
        onCompletion?()
    }
}

// MARK: - WindowManager Extension

extension WindowManager {
    /// Open the trial email collection modal
    func openTrialEmailCollection(onCompletion: @escaping () -> Void = {}) {
        // Close any existing trial modal first
        trialEmailWindowController?.window?.close()

        let windowController = TrialEmailWindowController(onCompletion: { [weak self] in
            print("🔍 TRIAL MODAL DEBUG: WindowController completion callback called")
            onCompletion()
            // Release the reference when modal closes
            self?.trialEmailWindowController = nil
        })

        // Keep a strong reference to prevent deallocation
        trialEmailWindowController = windowController
        windowController.showModal()
    }
}
