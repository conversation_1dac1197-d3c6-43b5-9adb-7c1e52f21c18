import Cocoa
import SwiftUI

/// Window controller for the license alert modal
class LicenseAlertWindowController: NSWindowController, NSWindowDelegate {
    private var onCompletion: (() -> Void)?

    convenience init(
        licenseStatus: LicenseStatus,
        onStartTrial: @escaping () -> Void,
        onPurchaseLicense: @escaping () -> Void,
        onEnterLicenseKey: @escaping () -> Void,
        onContinue: @escaping () -> Void,
        onCompletion: @escaping () -> Void
    ) {
        // Create the window
        let window = NSWindow(
            contentRect: NSRect(x: 0, y: 0, width: 450, height: 500),
            styleMask: [.titled, .closable],
            backing: .buffered,
            defer: false
        )

        // Configure window
        window.title = "Snapback"
        window.isReleasedWhenClosed = false
        window.center()
        window.level = .modalPanel
        window.isMovableByWindowBackground = true

        // Initialize with the window
        self.init(window: window)
        self.onCompletion = onCompletion

        // Create the SwiftUI view
        let licenseAlertView = LicenseAlertView(
            licenseStatus: licenseStatus,
            onStartTrial: { [weak self] in
                print("🔍 LICENSE ALERT DEBUG: Start Trial button tapped")
                onStartTrial()
                self?.closeModal()
            },
            onPurchaseLicense: { [weak self] in
                print("🔍 LICENSE ALERT DEBUG: Purchase License button tapped")
                onPurchaseLicense()
                self?.closeModal()
            },
            onEnterLicenseKey: { [weak self] in
                print("🔍 LICENSE ALERT DEBUG: Enter License Key button tapped")
                onEnterLicenseKey()
                self?.closeModal()
            },
            onContinue: { [weak self] in
                print("🔍 LICENSE ALERT DEBUG: Continue button tapped")
                onContinue()
                self?.closeModal()
            },
            onDismiss: { [weak self] in
                print("🔍 LICENSE ALERT DEBUG: Dismiss action called")
                self?.closeModal()
            }
        )

        // Set up the hosting view
        let hostingView = NSHostingView(rootView: licenseAlertView)
        window.contentView = hostingView

        // Make window non-resizable
        window.styleMask.remove(.resizable)

        // Set minimum and maximum size to prevent resizing
        window.minSize = NSSize(width: 450, height: 500)
        window.maxSize = NSSize(width: 450, height: 500)

        // Set window delegate to receive close notifications
        window.delegate = self
    }

    /// Show the modal window
    func showModal() {
        guard let window = window else { return }

        // Center the window
        window.center()

        // Show the window
        window.makeKeyAndOrderFront(nil)

        // Bring to front
        NSApp.activate(ignoringOtherApps: true)
    }

    /// Close the modal window
    private func closeModal() {
        print("🔍 LICENSE ALERT DEBUG: closeModal() called")
        window?.close()
        onCompletion?()
        print("🔍 LICENSE ALERT DEBUG: Modal window closed and completion called")
    }

    /// Handle window closing
    func windowWillClose(_ notification: Notification) {
        onCompletion?()
    }
}

// MARK: - WindowManager Extension

extension WindowManager {
    /// Open the license alert modal
    func openLicenseAlert(
        licenseStatus: LicenseStatus,
        onStartTrial: @escaping () -> Void = {},
        onPurchaseLicense: @escaping () -> Void = {},
        onEnterLicenseKey: @escaping () -> Void = {},
        onContinue: @escaping () -> Void = {},
        onCompletion: @escaping () -> Void = {}
    ) {
        // Close any existing license alert modal first
        licenseAlertWindowController?.window?.close()

        let windowController = LicenseAlertWindowController(
            licenseStatus: licenseStatus,
            onStartTrial: onStartTrial,
            onPurchaseLicense: onPurchaseLicense,
            onEnterLicenseKey: onEnterLicenseKey,
            onContinue: onContinue,
            onCompletion: { [weak self] in
                print("🔍 LICENSE ALERT DEBUG: WindowController completion callback called")
                onCompletion()
                // Release the reference when modal closes
                self?.licenseAlertWindowController = nil
            }
        )

        // Keep a strong reference to prevent deallocation
        licenseAlertWindowController = windowController
        windowController.showModal()
    }
}
