import Foundation

/// API service for license operations including trial requests and license validation
class LicenseAPIService {
    static let shared = LicenseAPIService()

    private init() {}

    // MARK: - Configuration

    /// Base URL for the license API (switch between mock and production)
    private var baseURL: String {
        #if DEBUG
            return "https://mock-api.snapback.dev"  // Mock API for development
        #else
            return "https://api.snapback.dev"  // Production API
        #endif
    }

    // MARK: - API Models

    /// Request model for trial license request
    struct TrialRequest: Codable {
        let email: String
        let deviceId: String
        let appVersion: String
    }

    /// Response model for trial license request
    struct TrialResponse: Codable {
        let success: Bool
        let licenseKey: String?
        let expirationDate: String?  // ISO 8601 format
        let message: String?
        let error: String?
    }

    /// Request model for license validation
    struct ValidationRequest: Codable {
        let licenseKey: String
        let email: String
        let deviceId: String
    }

    /// Response model for license validation
    struct ValidationResponse: Codable {
        let valid: Bool
        let licenseType: String?
        let registeredUser: String?
        let email: String?
        let expirationDate: String?  // ISO 8601 format
        let features: [String]?
        let message: String?
        let error: String?
    }

    // MARK: - API Methods

    /// Request a trial license for the given email
    func requestTrialLicense(email: String) async throws -> TrialResponse {
        let deviceId = getDeviceIdentifier()
        let appVersion = getAppVersion()

        let request = TrialRequest(
            email: email,
            deviceId: deviceId,
            appVersion: appVersion
        )

        // For development, use mock response
        #if DEBUG
            return try await mockTrialRequest(request)
        #else
            return try await performTrialRequest(request)
        #endif
    }

    /// Validate a license key with email
    func validateLicense(licenseKey: String, email: String) async throws -> ValidationResponse {
        let deviceId = getDeviceIdentifier()

        let request = ValidationRequest(
            licenseKey: licenseKey,
            email: email,
            deviceId: deviceId
        )

        // For development, use mock response
        #if DEBUG
            return try await mockValidationRequest(request)
        #else
            return try await performValidationRequest(request)
        #endif
    }

    // MARK: - Mock Implementation (Development)

    /// Mock trial request for development
    private func mockTrialRequest(_ request: TrialRequest) async throws -> TrialResponse {
        // Simulate network delay
        try await Task.sleep(nanoseconds: 1_000_000_000)  // 1 second

        // Validate email format
        guard isValidEmail(request.email) else {
            return TrialResponse(
                success: false,
                licenseKey: nil,
                expirationDate: nil,
                message: nil,
                error: "Invalid email format"
            )
        }

        // Generate a mock trial license key
        let trialKey = generateMockTrialKey()

        // Calculate expiration date (15 days from now)
        let expirationDate = Calendar.current.date(byAdding: .day, value: 15, to: Date())!
        let isoFormatter = ISO8601DateFormatter()

        return TrialResponse(
            success: true,
            licenseKey: trialKey,
            expirationDate: isoFormatter.string(from: expirationDate),
            message: "Trial license generated successfully",
            error: nil
        )
    }

    /// Mock validation request for development
    private func mockValidationRequest(_ request: ValidationRequest) async throws
        -> ValidationResponse
    {
        // Simulate network delay
        try await Task.sleep(nanoseconds: 500_000_000)  // 0.5 seconds

        // Check if it's a mock trial key
        if request.licenseKey.hasPrefix("TRIAL-") {
            let expirationDate = Calendar.current.date(byAdding: .day, value: 15, to: Date())!
            let isoFormatter = ISO8601DateFormatter()

            return ValidationResponse(
                valid: true,
                licenseType: "Trial",
                registeredUser: request.email,
                email: request.email,
                expirationDate: isoFormatter.string(from: expirationDate),
                features: [
                    "All Workspace Features", "Unlimited Workspaces",
                    "Window Management", "Keyboard Shortcuts", "CloudKit Sync",
                ],
                message: "Valid trial license",
                error: nil
            )
        }

        // Check existing demo keys with email validation
        // Remove dashes and convert to lowercase for comparison
        let cleanedKey = request.licenseKey.replacingOccurrences(of: "-", with: "").lowercased()

        switch cleanedKey {
        case "snapback2025life":
            return ValidationResponse(
                valid: true,
                licenseType: "Lifetime License",
                registeredUser: request.email,
                email: request.email,
                expirationDate: nil,
                features: [
                    "All Workspace Features", "Unlimited Workspaces",
                    "Window Management", "Keyboard Shortcuts", "CloudKit Sync",
                ],
                message: "Valid lifetime license",
                error: nil
            )

        case "snapback2025year":
            let expirationDate = Calendar.current.date(byAdding: .year, value: 1, to: Date())!
            let isoFormatter = ISO8601DateFormatter()

            return ValidationResponse(
                valid: true,
                licenseType: "Annual License",
                registeredUser: request.email,
                email: request.email,
                expirationDate: isoFormatter.string(from: expirationDate),
                features: [
                    "All Workspace Features", "Unlimited Workspaces",
                    "Window Management", "Keyboard Shortcuts", "CloudKit Sync",
                ],
                message: "Valid annual license",
                error: nil
            )

        case "test2025expr":
            // Standard format expired license test key: TEST-2025-EXPR-IRED
            let expiredDate = Calendar.current.date(byAdding: .day, value: -30, to: Date())!
            let isoFormatter = ISO8601DateFormatter()

            return ValidationResponse(
                valid: false,
                licenseType: "Expired Annual License",
                registeredUser: request.email,
                email: request.email,
                expirationDate: isoFormatter.string(from: expiredDate),
                features: ["License Expired"],
                message: nil,
                error:
                    "Your license has expired. Please purchase a new license or enter a valid license key."
            )

        default:
            return ValidationResponse(
                valid: false,
                licenseType: nil,
                registeredUser: nil,
                email: nil,
                expirationDate: nil,
                features: nil,
                message: nil,
                error: "Invalid license key"
            )
        }
    }

    // MARK: - Production Implementation

    /// Perform actual trial request to production API
    private func performTrialRequest(_ request: TrialRequest) async throws -> TrialResponse {
        let url = URL(string: "\(baseURL)/api/trial/request")!

        var urlRequest = URLRequest(url: url)
        urlRequest.httpMethod = "POST"
        urlRequest.setValue("application/json", forHTTPHeaderField: "Content-Type")
        urlRequest.httpBody = try JSONEncoder().encode(request)

        let (data, response) = try await URLSession.shared.data(for: urlRequest)

        guard let httpResponse = response as? HTTPURLResponse,
            httpResponse.statusCode == 200
        else {
            throw APIError.networkError("Failed to request trial license")
        }

        return try JSONDecoder().decode(TrialResponse.self, from: data)
    }

    /// Perform actual validation request to production API
    private func performValidationRequest(_ request: ValidationRequest) async throws
        -> ValidationResponse
    {
        let url = URL(string: "\(baseURL)/api/license/validate")!

        var urlRequest = URLRequest(url: url)
        urlRequest.httpMethod = "POST"
        urlRequest.setValue("application/json", forHTTPHeaderField: "Content-Type")
        urlRequest.httpBody = try JSONEncoder().encode(request)

        let (data, response) = try await URLSession.shared.data(for: urlRequest)

        guard let httpResponse = response as? HTTPURLResponse,
            httpResponse.statusCode == 200
        else {
            throw APIError.networkError("Failed to validate license")
        }

        return try JSONDecoder().decode(ValidationResponse.self, from: data)
    }

    // MARK: - Helper Methods

    /// Generate a mock trial license key
    private func generateMockTrialKey() -> String {
        let timestamp = String(Int(Date().timeIntervalSince1970))
        let randomSuffix = String(Int.random(in: 1000...9999))
        return "TRIAL-\(timestamp)-\(randomSuffix)"
    }

    /// Get device identifier
    private func getDeviceIdentifier() -> String {
        // Use a persistent device identifier
        let key = "SnapbackDeviceIdentifier"
        if let existing = UserDefaults.standard.string(forKey: key) {
            return existing
        }

        let newId = UUID().uuidString
        UserDefaults.standard.set(newId, forKey: key)
        return newId
    }

    /// Get app version
    private func getAppVersion() -> String {
        return Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0.0"
    }

    /// Validate email format
    private func isValidEmail(_ email: String) -> Bool {
        let emailRegex = "^[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$"
        let emailPredicate = NSPredicate(format: "SELF MATCHES %@", emailRegex)
        return emailPredicate.evaluate(with: email)
    }
}

// MARK: - API Errors

enum APIError: Error, LocalizedError {
    case networkError(String)
    case invalidResponse
    case invalidEmail
    case serverError(String)

    var errorDescription: String? {
        switch self {
        case .networkError(let message):
            return "Network error: \(message)"
        case .invalidResponse:
            return "Invalid response from server"
        case .invalidEmail:
            return "Invalid email address"
        case .serverError(let message):
            return "Server error: \(message)"
        }
    }
}
