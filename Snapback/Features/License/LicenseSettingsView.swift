import SwiftUI

struct LicenseSettingsView: View {
    @StateObject private var licenseManager = LicenseManager.shared
    @EnvironmentObject private var appDelegate: AppDelegate
    @State private var licenseKeyInput: String = ""
    @State private var emailInput: String = ""
    @State private var showingClearConfirmation = false

    var body: some View {
        VStack(spacing: 0) {
            ScrollView {
                VStack(alignment: .leading, spacing: 16) {
                    // License Status Section
                    Text("License Status")
                        .snapbackSectionTitleStyle()

                    GroupBox {
                        VStack(spacing: 0) {
                            // Current Status Row
                            HStack {
                                Image(systemName: statusIcon)
                                    .foregroundColor(licenseManager.licenseStatus.color)
                                    .frame(width: 16)

                                Text("Status")

                                Spacer()

                                Text(licenseManager.licenseStatus.displayName)
                                    .foregroundColor(licenseManager.licenseStatus.color)
                                    .font(
                                        .system(size: SnapbackTheme.FontSize.body, weight: .medium))
                            }
                            .snapbackRowStyle()

                            // Trial Information (if on active trial)
                            if licenseManager.licenseStatus == .trial
                                && licenseManager.isTrialActive
                            {
                                Divider()

                                HStack {
                                    Image(systemName: "clock")
                                        .foregroundColor(.orange)
                                        .frame(width: 16)

                                    Text("Trial Days Remaining")

                                    Spacer()

                                    Text("\(licenseManager.remainingTrialDays) days")
                                        .foregroundColor(.orange)
                                        .font(
                                            .system(
                                                size: SnapbackTheme.FontSize.body, weight: .medium))
                                }
                                .snapbackRowStyle()
                            }

                            // License Information (if available)
                            if let info = licenseManager.licenseInfo {
                                Divider()

                                VStack(spacing: 0) {
                                    // License Type
                                    HStack {
                                        Image(systemName: "doc.text")
                                            .foregroundColor(.secondary)
                                            .frame(width: 16)

                                        Text("License Type")

                                        Spacer()

                                        Text(info.licenseType)
                                            .foregroundColor(.secondary)
                                    }
                                    .snapbackRowStyle()

                                    Divider()

                                    // Registered User
                                    HStack {
                                        Image(systemName: "person")
                                            .foregroundColor(.secondary)
                                            .frame(width: 16)

                                        Text("Registered To")

                                        Spacer()

                                        Text(info.registeredUser)
                                            .foregroundColor(.secondary)
                                    }
                                    .snapbackRowStyle()

                                    Divider()

                                    // Expiration Date
                                    HStack {
                                        Image(systemName: "calendar")
                                            .foregroundColor(.secondary)
                                            .frame(width: 16)

                                        Text("Expires")

                                        Spacer()

                                        Text(info.expirationDisplayText)
                                            .foregroundColor(info.isExpired ? .red : .secondary)
                                    }
                                    .snapbackRowStyle()
                                }
                            }
                        }
                    }

                    // License Key Section
                    Text("License Information")
                        .snapbackSectionTitleStyle()

                    GroupBox {
                        VStack(spacing: 0) {
                            // Email Input Row - following General tab pattern
                            VStack(alignment: .leading, spacing: 4) {
                                HStack {
                                    Text("Email Address")

                                    Spacer()

                                    if licenseManager.isValidating {
                                        ProgressView()
                                            .scaleEffect(0.8)
                                    } else {
                                        TextField("<EMAIL>", text: $emailInput)
                                            .textFieldStyle(.roundedBorder)
                                            .disabled(
                                                licenseManager.isValidating
                                                    || (licenseManager.licenseStatus == .valid
                                                        && !licenseManager.licenseKey.isEmpty)
                                                    || (licenseManager.licenseStatus == .trial
                                                        && licenseManager.isTrialActive)
                                            )
                                            .disableAutocorrection(true)
                                            .textContentType(.emailAddress)
                                            .frame(width: 200)
                                    }
                                }

                                if (licenseManager.licenseStatus == .valid
                                    && !licenseManager.licenseKey.isEmpty)
                                    || (licenseManager.licenseStatus == .trial
                                        && licenseManager.isTrialActive)
                                {
                                    Text(
                                        licenseManager.licenseStatus == .trial
                                            ? "Email and license key are locked during active trial period."
                                            : "Email and license key are locked when a valid license is active."
                                    )
                                    .snapbackCaptionStyle()
                                }
                            }
                            .snapbackRowStyle()

                            Divider()

                            // License Key Input Row - following General tab pattern
                            VStack(alignment: .leading, spacing: 4) {
                                HStack {
                                    Text("License Key")

                                    Spacer()

                                    TextField("XXXX-XXXX-XXXX-XXXX", text: $licenseKeyInput)
                                        .textFieldStyle(.roundedBorder)
                                        .font(.system(.body, design: .monospaced))
                                        .disabled(
                                            licenseManager.isValidating
                                                || (licenseManager.licenseStatus == .valid
                                                    && !licenseManager.licenseKey.isEmpty)
                                                || (licenseManager.licenseStatus == .trial
                                                    && licenseManager.isTrialActive)
                                        )
                                        .disableAutocorrection(true)
                                        .frame(width: 200)
                                        .onSubmit {
                                            Task {
                                                await licenseManager.setLicenseKeyAndEmail(
                                                    licenseKeyInput, email: emailInput)
                                            }
                                        }
                                        .onChange(of: licenseKeyInput) { oldValue, newValue in
                                            // Auto-format license key with dashes, but preserve trial key format
                                            if newValue.hasPrefix("TRIAL-") {
                                                // Don't reformat trial keys - they have their own format
                                                return
                                            }

                                            let cleaned = newValue.replacingOccurrences(
                                                of: "-", with: ""
                                            ).uppercased()
                                            if cleaned.count <= 16 {
                                                let formatted = formatLicenseKey(cleaned)
                                                if formatted != newValue {
                                                    licenseKeyInput = formatted
                                                }
                                            } else {
                                                licenseKeyInput = oldValue
                                            }
                                        }
                                }
                            }
                            .snapbackRowStyle()

                            Divider()

                            // Action Buttons Section - following General tab pattern
                            VStack(alignment: .leading, spacing: 4) {
                                // Error Message
                                if let error = licenseManager.lastError {
                                    Text(error)
                                        .snapbackCaptionStyle()
                                        .foregroundColor(.red)
                                        .padding(.bottom, 8)
                                }

                                // Action Buttons - show based on license status
                                if licenseManager.licenseStatus == .valid
                                    || (licenseManager.licenseStatus == .trial
                                        && licenseManager.isTrialActive)
                                {
                                    // Show unregister button for valid licenses and active trials
                                    HStack {
                                        Button("Unregister") {
                                            showingClearConfirmation = true
                                        }
                                        .buttonStyle(.bordered)
                                        .foregroundColor(.red)

                                        Spacer()
                                    }
                                } else if licenseManager.licenseStatus != .valid
                                    && !(licenseManager.licenseStatus == .trial
                                        && licenseManager.isTrialActive)
                                {
                                    // Show validate and unregister buttons for non-licensed states
                                    HStack(spacing: 12) {
                                        // Only show Validate button when not already licensed or on trial
                                        Button("Validate License") {
                                            Task {
                                                await licenseManager.setLicenseKeyAndEmail(
                                                    licenseKeyInput, email: emailInput)
                                            }
                                        }
                                        .buttonStyle(.bordered)
                                        .disabled(
                                            licenseKeyInput.isEmpty || emailInput.isEmpty
                                                || licenseManager.isValidating)

                                        // Unregister Button (only show if there's a license key)
                                        if !licenseManager.licenseKey.isEmpty {
                                            Button("Unregister") {
                                                showingClearConfirmation = true
                                            }
                                            .buttonStyle(.bordered)
                                            .foregroundColor(.red)
                                        }

                                        Spacer()
                                    }
                                }
                            }
                            .snapbackRowStyle()
                        }
                    }

                    // Help Section
                    Text("Help")
                        .snapbackSectionTitleStyle()

                    GroupBox {
                        VStack(spacing: 0) {
                            VStack(alignment: .leading, spacing: 4) {
                                Text("Need help with licensing?")
                                    .font(
                                        .system(size: SnapbackTheme.FontSize.body, weight: .medium))

                                Text(
                                    "Contact support for license issues, visit our website for purchasing options, or check your email for license key delivery."
                                )
                                .snapbackCaptionStyle()

                                helpButtonsView
                                    .padding(.top, 8)
                            }
                            .snapbackRowStyle()
                        }
                    }

                    Spacer(minLength: 20)
                }
                .padding()
            }

            // Footer with Unregister Button
            SettingsFooter(buttonTitle: "Unregister License") {
                showingClearConfirmation = true
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .onAppear {
            // Load current license key and email into input fields
            licenseKeyInput = licenseManager.licenseKey
            emailInput = licenseManager.userEmail
        }
        .onChange(of: licenseManager.licenseKey) { _, newKey in
            // Update input field when license key changes (e.g., trial activation)
            licenseKeyInput = newKey
        }
        .onChange(of: licenseManager.userEmail) { _, newEmail in
            // Update input field when email changes (e.g., trial activation)
            emailInput = newEmail
        }
        .alert("Unregister License", isPresented: $showingClearConfirmation) {
            Button("Cancel", role: .cancel) {}
            Button("Unregister", role: .destructive) {
                licenseManager.clearLicense()
                licenseKeyInput = ""
                emailInput = ""
            }
        } message: {
            Text(
                "Are you sure you want to unregister this license? This will remove the license from this device. You can re-register it later using the same license key."
            )
        }
    }

    // MARK: - Helper Properties

    private var statusIcon: String {
        switch licenseManager.licenseStatus {
        case .unlicensed:
            return "exclamationmark.triangle"
        case .valid:
            return "checkmark.circle.fill"
        case .invalid:
            return "xmark.circle.fill"
        case .expired:
            return "clock.badge.exclamationmark"
        case .trial:
            return "clock"
        }
    }

    // MARK: - Helper Views

    private var helpButtonsView: some View {
        HStack(spacing: 12) {
            Button("Contact Support") {
                if let url = URL(string: "mailto:<EMAIL>") {
                    NSWorkspace.shared.open(url)
                }
            }
            .buttonStyle(.bordered)

            Button("Purchase License") {
                if let url = URL(string: "https://snapbackapp.com/purchase") {
                    NSWorkspace.shared.open(url)
                }
            }
            .buttonStyle(.bordered)

            // Start Trial Button (only show if not licensed and no active trial)
            if licenseManager.licenseStatus == .unlicensed
                || (licenseManager.licenseStatus == .invalid
                    && licenseManager.licenseKey.isEmpty)
            {
                Button("Start Free Trial") {
                    appDelegate.windowManager.openTrialEmailCollection()
                }
                .buttonStyle(.borderedProminent)
            }

            Spacer()
        }
        .padding(.top, 8)
    }
}

// MARK: - Helper Methods

/// Format license key with dashes (XXXX-XXXX-XXXX-XXXX)
/// Preserves trial key format (TRIAL-timestamp-suffix)
private func formatLicenseKey(_ key: String) -> String {
    // Don't reformat trial keys - they have their own format
    if key.hasPrefix("TRIAL-") {
        return key
    }

    let cleaned = key.replacingOccurrences(of: "-", with: "")
    var formatted = ""

    for (index, character) in cleaned.enumerated() {
        if index > 0 && index % 4 == 0 {
            formatted += "-"
        }
        formatted += String(character)
    }

    return formatted
}

#Preview {
    LicenseSettingsView()
        .frame(width: 600, height: 500)
        .background(SnapbackTheme.Background.window)
}
